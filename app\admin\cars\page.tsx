"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Search, MoreVertical, Eye, Edit, Trash, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { toast } from "sonner"

export default function CarsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [cars, setCars] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient()

  // Load cars from database
  useEffect(() => {
    loadCars()
  }, [])

  const loadCars = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('cars')
        .select(`
          *,
          agencies (
            agency_name
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading cars:', error)
        toast.error('Failed to load cars')
        return
      }

      // Transform data to match the expected format
      const transformedCars = data.map(car => ({
        id: car.id,
        name: `${car.brand} ${car.model}`,
        agency: car.agencies?.agency_name || 'Unknown Agency',
        price: car.daily_rate || 0,
        status: car.status || 'active',
        bookings: 0, // We'll need to count bookings separately
        year: car.year || 'N/A',
        category: car.category || 'Economy',
        brand: car.brand,
        model: car.model,
        color: car.color,
        transmission: car.transmission,
        fuel_type: car.fuel_type,
        mileage: car.mileage,
        images: car.images,
      }))

      setCars(transformedCars)
    } catch (error) {
      console.error('Error loading cars:', error)
      toast.error('Failed to load cars')
    } finally {
      setLoading(false)
    }
  }



  const filteredCars = cars.filter(
    (car) =>
      car.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      car.agency.toLowerCase().includes(searchQuery.toLowerCase()) ||
      car.category.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "pending":
        return <Badge className="bg-amber-500">Pending</Badge>
      case "inactive":
        return <Badge className="bg-red-500">Inactive</Badge>
      default:
        return <Badge>Unknown</Badge>
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Cars Management</h1>
          <p className="text-muted-foreground">Manage all cars listed on the platform</p>
        </div>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>All Cars</CardTitle>
          <CardDescription>View and manage all cars listed on the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search cars..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Export
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="py-3 px-4 text-left font-medium">Car</th>
                  <th className="py-3 px-4 text-left font-medium">Agency</th>
                  <th className="py-3 px-4 text-left font-medium">Category</th>
                  <th className="py-3 px-4 text-left font-medium">Year</th>
                  <th className="py-3 px-4 text-left font-medium">Price/Day</th>
                  <th className="py-3 px-4 text-left font-medium">Status</th>
                  <th className="py-3 px-4 text-left font-medium">Bookings</th>
                  <th className="py-3 px-4 text-left font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={8} className="py-8 text-center">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                      <p>Loading cars...</p>
                    </td>
                  </tr>
                ) : filteredCars.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="py-8 text-center text-gray-500">
                      No cars found
                    </td>
                  </tr>
                ) : filteredCars.map((car) => (
                  <tr key={car.id} className="border-b">
                    <td className="py-3 px-4 font-medium">{car.name}</td>
                    <td className="py-3 px-4">{car.agency}</td>
                    <td className="py-3 px-4">{car.category}</td>
                    <td className="py-3 px-4">{car.year}</td>
                    <td className="py-3 px-4">${car.price}</td>
                    <td className="py-3 px-4">{getStatusBadge(car.status)}</td>
                    <td className="py-3 px-4">{car.bookings}</td>
                    <td className="py-3 px-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Car
                          </DropdownMenuItem>
                          {car.status === "pending" && (
                            <>
                              <DropdownMenuItem>
                                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                Approve Listing
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <XCircle className="h-4 w-4 mr-2 text-red-500" />
                                Reject Listing
                              </DropdownMenuItem>
                            </>
                          )}
                          {car.status === "active" && (
                            <DropdownMenuItem>
                              <XCircle className="h-4 w-4 mr-2 text-amber-500" />
                              Deactivate
                            </DropdownMenuItem>
                          )}
                          {car.status === "inactive" && (
                            <DropdownMenuItem>
                              <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                              Activate
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem className="text-red-500">
                            <Trash className="h-4 w-4 mr-2" />
                            Delete Car
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

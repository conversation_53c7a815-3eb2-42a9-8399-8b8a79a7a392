'use client'

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog"
import { Edit, Trash, Eye, Globe, FileText, Loader2 } from "lucide-react"
import { DatabaseService } from "@/services/database.service"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import dynamic from "next/dynamic"
import "react-quill/dist/quill.snow.css"

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false })

interface BlogFormState {
    title: string
    content: string
    excerpt: string
    image: File | null
    tags: string
    status: 'draft' | 'published'
}

export default function AdminBlogsPage() {
    const [form, setForm] = useState<BlogFormState>({
        title: "",
        content: "",
        excerpt: "",
        image: null,
        tags: "",
        status: 'draft'
    })
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [preview, setPreview] = useState<string | null>(null)
    const [blogs, setBlogs] = useState<any[]>([])
    const [loading, setLoading] = useState(true)
    const [editingBlog, setEditingBlog] = useState<any>(null)
    const [showEditDialog, setShowEditDialog] = useState(false)
    const [showDeleteDialog, setShowDeleteDialog] = useState(false)
    const [blogToDelete, setBlogToDelete] = useState<string | null>(null)
    const router = useRouter()
    const { user } = useAuth()

    // Load blogs on component mount
    useEffect(() => {
        loadBlogs()
    }, [])

    const loadBlogs = async () => {
        try {
            setLoading(true)
            const { data, error } = await DatabaseService.getBlogs()
            if (error) {
                console.error('Error loading blogs:', error)
                toast.error('Failed to load blogs')
                return
            }
            setBlogs(data || [])
        } catch (error) {
            console.error('Error loading blogs:', error)
            toast.error('Failed to load blogs')
        } finally {
            setLoading(false)
        }
    }

    function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
        const { name, value } = e.target
        setForm((prev) => ({ ...prev, [name]: value }))
    }

    function handleContentChange(value: string) {
        setForm((prev) => ({ ...prev, content: value }))
    }

    function handleImageChange(e: React.ChangeEvent<HTMLInputElement>) {
        const file = e.target.files?.[0] || null
        setForm((prev) => ({ ...prev, image: file }))
        if (file) setPreview(URL.createObjectURL(file))
        else setPreview(null)
    }

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault()
        setIsSubmitting(true)

        try {
            if (!form.title || !form.content) {
                toast.error("Please fill in title and content")
                return
            }

            // Create slug from title
            const slug = form.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')

            // Parse tags
            const tags = form.tags
                .split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0)

            // Create blog post
            const { error } = await DatabaseService.createBlog({
                title: form.title,
                content: form.content,
                excerpt: form.excerpt || form.content.substring(0, 200) + '...',
                slug,
                status: form.status,
                author_id: user?.id,
                tags,
                published_at: form.status === 'published' ? new Date().toISOString() : null
            })

            if (error) {
                console.error('Error creating blog:', error)
                toast.error('Failed to create blog post')
                return
            }

            toast.success(`Blog post ${form.status === 'published' ? 'published' : 'saved as draft'} successfully!`)
            setForm({
                title: "",
                content: "",
                excerpt: "",
                image: null,
                tags: "",
                status: 'draft'
            })
            setPreview(null)
            loadBlogs() // Reload the blogs list

        } catch (error) {
            console.error('Error creating blog:', error)
            toast.error('Failed to create blog post')
        } finally {
            setIsSubmitting(false)
        }
    }

    const handlePublishBlog = async (blogId: string) => {
        try {
            const { error } = await DatabaseService.updateBlog(blogId, {
                status: 'published',
                published_at: new Date().toISOString()
            })

            if (error) {
                toast.error('Failed to publish blog')
                return
            }

            toast.success('Blog published successfully!')
            loadBlogs()
        } catch (error) {
            console.error('Error publishing blog:', error)
            toast.error('Failed to publish blog')
        }
    }

    const handleUnpublishBlog = async (blogId: string) => {
        try {
            const { error } = await DatabaseService.updateBlog(blogId, {
                status: 'draft',
                published_at: null
            })

            if (error) {
                toast.error('Failed to unpublish blog')
                return
            }

            toast.success('Blog unpublished successfully!')
            loadBlogs()
        } catch (error) {
            console.error('Error unpublishing blog:', error)
            toast.error('Failed to unpublish blog')
        }
    }

    const handleDeleteBlog = async () => {
        if (!blogToDelete) return

        try {
            const { error } = await DatabaseService.deleteBlog(blogToDelete)

            if (error) {
                toast.error('Failed to delete blog')
                return
            }

            toast.success('Blog deleted successfully!')
            loadBlogs()
            setShowDeleteDialog(false)
            setBlogToDelete(null)
        } catch (error) {
            console.error('Error deleting blog:', error)
            toast.error('Failed to delete blog')
        }
    }

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'published':
                return <Badge className="bg-green-500">Published</Badge>
            case 'draft':
                return <Badge className="bg-yellow-500">Draft</Badge>
            case 'archived':
                return <Badge className="bg-gray-500">Archived</Badge>
            default:
                return <Badge>Unknown</Badge>
        }
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-3xl font-bold mb-6">Create Blog Post</h1>
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block mb-2 font-medium">Title *</label>
                        <Input
                            name="title"
                            placeholder="Enter blog title"
                            value={form.title}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <div>
                        <label className="block mb-2 font-medium">Status</label>
                        <select
                            name="status"
                            value={form.status}
                            onChange={(e) => setForm(prev => ({ ...prev, status: e.target.value as 'draft' | 'published' }))}
                            className="w-full p-2 border rounded-md"
                        >
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label className="block mb-2 font-medium">Excerpt</label>
                    <Input
                        name="excerpt"
                        placeholder="Brief description of the blog post"
                        value={form.excerpt}
                        onChange={handleChange}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Tags</label>
                    <Input
                        name="tags"
                        placeholder="Enter tags separated by commas (e.g., travel, cars, tips)"
                        value={form.tags}
                        onChange={handleChange}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Content *</label>
                    <ReactQuill
                        value={form.content}
                        onChange={handleContentChange}
                        theme="snow"
                        className="bg-white"
                        style={{ minHeight: 300 }}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Featured Image</label>
                    <Input type="file" accept="image/*" onChange={handleImageChange} />
                    {preview && (
                        <img src={preview} alt="Preview" className="mt-4 rounded w-full max-h-64 object-cover" />
                    )}
                </div>
                <div className="flex gap-4">
                    <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? "Saving..." : form.status === 'published' ? "Publish Blog" : "Save Draft"}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.back()}>
                        Cancel
                    </Button>
                </div>
            </form>

            {/* Blogs List */}
            <Card className="mt-8">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        All Blog Posts
                    </CardTitle>
                    <CardDescription>
                        Manage your blog posts - edit, delete, or publish them
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin mr-2" />
                            <span>Loading blogs...</span>
                        </div>
                    ) : blogs.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            No blog posts created yet. Create your first blog post above!
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {blogs.map((blog) => (
                                <div key={blog.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-2">
                                                <h3 className="font-semibold text-lg">{blog.title}</h3>
                                                {getStatusBadge(blog.status)}
                                            </div>
                                            <p className="text-gray-600 text-sm mb-2">
                                                {blog.excerpt || blog.content?.substring(0, 150) + '...'}
                                            </p>
                                            <div className="flex items-center gap-4 text-xs text-gray-500">
                                                <span>Created: {new Date(blog.created_at).toLocaleDateString()}</span>
                                                {blog.published_at && (
                                                    <span>Published: {new Date(blog.published_at).toLocaleDateString()}</span>
                                                )}
                                                {blog.tags && blog.tags.length > 0 && (
                                                    <span>Tags: {blog.tags.join(', ')}</span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2 ml-4">
                                            {blog.status === 'published' ? (
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => handleUnpublishBlog(blog.id)}
                                                >
                                                    <Eye className="h-4 w-4 mr-1" />
                                                    Unpublish
                                                </Button>
                                            ) : (
                                                <Button
                                                    size="sm"
                                                    onClick={() => handlePublishBlog(blog.id)}
                                                >
                                                    <Globe className="h-4 w-4 mr-1" />
                                                    Publish
                                                </Button>
                                            )}
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    setEditingBlog(blog)
                                                    setShowEditDialog(true)
                                                }}
                                            >
                                                <Edit className="h-4 w-4 mr-1" />
                                                Edit
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="destructive"
                                                onClick={() => {
                                                    setBlogToDelete(blog.id)
                                                    setShowDeleteDialog(true)
                                                }}
                                            >
                                                <Trash className="h-4 w-4 mr-1" />
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Blog Post</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this blog post? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteBlog}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
} 
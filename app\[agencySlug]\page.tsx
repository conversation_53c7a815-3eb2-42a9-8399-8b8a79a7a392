'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  MapPin, 
  Phone, 
  Mail, 
  Star, 
  Car, 
  Clock, 
  Shield,
  Fuel,
  Users,
  Calendar,
  CheckCircle
} from "lucide-react"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { toast } from "sonner"
import Link from "next/link"

interface Agency {
  id: string
  agency_name: string
  agency_email: string
  agency_phone: string
  agency_description: string
  agency_logo: string
  location: string
  rating: number
  is_approved: boolean
  operating_hours_start: string
  operating_hours_end: string
  created_at: string
}

interface Car {
  id: string
  brand: string
  model: string
  year: number
  daily_rate: number
  security_deposit: number
  fuel_type: string
  transmission: string
  seats: number
  color: string
  images: string[]
  status: string
}

export default function AgencyPublicProfilePage() {
  const params = useParams()
  const agencySlug = params.agencySlug as string
  const [agency, setAgency] = useState<Agency | null>(null)
  const [cars, setCars] = useState<Car[]>([])
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)
  const supabase = createClientComponentClient()

  useEffect(() => {
    if (agencySlug) {
      loadAgencyProfile()
    }
  }, [agencySlug])

  const loadAgencyProfile = async () => {
    try {
      setLoading(true)
      
      // Convert slug back to agency name (reverse the slug process)
      const agencyName = agencySlug.replace(/-/g, ' ')
      
      // Find agency by name (case insensitive)
      const { data: agencyData, error: agencyError } = await supabase
        .from('agencies')
        .select('*')
        .ilike('agency_name', agencyName)
        .eq('is_approved', true)
        .single()

      if (agencyError || !agencyData) {
        console.error('Agency not found:', agencyError)
        setNotFound(true)
        return
      }

      setAgency(agencyData)

      // Load agency's cars
      const { data: carsData, error: carsError } = await supabase
        .from('cars')
        .select('*')
        .eq('agency_id', agencyData.id)
        .eq('status', 'available')
        .order('created_at', { ascending: false })

      if (carsError) {
        console.error('Error loading cars:', carsError)
      } else {
        setCars(carsData || [])
      }

    } catch (error) {
      console.error('Error loading agency profile:', error)
      setNotFound(true)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading agency profile...</p>
        </div>
      </div>
    )
  }

  if (notFound || !agency) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Agency Not Found</h1>
          <p className="text-gray-600 mb-8">The agency you're looking for doesn't exist or is not approved.</p>
          <Link href="/">
            <Button>Back to Home</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-start space-x-6">
            <Avatar className="h-24 w-24">
              <AvatarImage src={agency.agency_logo} alt={agency.agency_name} />
              <AvatarFallback className="text-2xl">
                {agency.agency_name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{agency.agency_name}</h1>
                {agency.is_approved && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 text-gray-600 mb-4">
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span className="capitalize">{agency.location}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span>{agency.rating || 4.5}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Car className="h-4 w-4" />
                  <span>{cars.length} Cars Available</span>
                </div>
              </div>
              <p className="text-gray-700 max-w-2xl">
                {agency.agency_description || 'Professional car rental services in Morocco.'}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Cars Section */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Cars</h2>
              {cars.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No cars available at the moment.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {cars.map((car) => (
                    <Card key={car.id} className="hover:shadow-lg transition-shadow">
                      <div className="relative h-48 overflow-hidden rounded-t-lg">
                        <img
                          src={car.images?.[0] || '/placeholder.svg?height=200&width=300'}
                          alt={`${car.brand} ${car.model}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <CardHeader>
                        <CardTitle className="text-xl">{car.brand} {car.model}</CardTitle>
                        <CardDescription>{car.year} • {car.color}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                          <div className="flex items-center space-x-2">
                            <Fuel className="h-4 w-4 text-gray-500" />
                            <span>{car.fuel_type}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-500" />
                            <span>{car.seats} seats</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Shield className="h-4 w-4 text-gray-500" />
                            <span>{car.transmission}</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-blue-600">د.م {car.daily_rate}</p>
                            <p className="text-sm text-gray-500">per day</p>
                          </div>
                          <Button asChild>
                            <Link href={`/car/${car.id}`}>
                              View Details
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {agency.agency_phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-gray-600">{agency.agency_phone}</p>
                    </div>
                  </div>
                )}
                {agency.agency_email && (
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-gray-600">{agency.agency_email}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Business Hours */}
            {(agency.operating_hours_start && agency.operating_hours_end) && (
              <Card>
                <CardHeader>
                  <CardTitle>Business Hours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Daily</p>
                      <p className="text-gray-600">
                        {agency.operating_hours_start} - {agency.operating_hours_end}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Member Since */}
            <Card>
              <CardHeader>
                <CardTitle>Agency Info</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium">Member Since</p>
                    <p className="text-gray-600">
                      {new Date(agency.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

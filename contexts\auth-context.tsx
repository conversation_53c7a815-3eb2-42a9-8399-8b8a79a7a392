"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { supabase } from '@/lib/supabase/client'
import { SupabaseAuthService } from '@/services/supabase-auth.service'
import { User as SupabaseUser, Session } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

type UserProfile = Database['public']['Tables']['users']['Row']

interface AuthContextType {
    user: (SupabaseUser & UserProfile) | null
    session: Session | null
    userProfile: UserProfile | null
    login: (email: string, password: string) => Promise<{ error: any }>
    signUp: (email: string, password: string, userData?: Partial<UserProfile>) => Promise<{ error: any }>
    logout: () => Promise<void>
    updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>
    updateUser: (updates: Partial<UserProfile>) => Promise<{ error: any }>
    verifyEmail: (token: string) => Promise<{ error: any }>
    markNotificationAsRead: (notificationId: string) => Promise<void>
    isAuthenticated: boolean
    isLoading: boolean
    isAgency: boolean
    isAdmin: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
    const [user, setUser] = useState<(SupabaseUser & UserProfile) | null>(null)
    const [session, setSession] = useState<Session | null>(null)
    const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const router = useRouter()

    // Helper to fetch user profile from public.users
    const fetchUserProfile = async (userId: string) => {
        try {
            const { data, error } = await SupabaseAuthService.getUserProfile(userId)
            if (error) {
                console.error('❌ Error fetching user profile:', error)
                return null
            }
            return data
        } catch (error) {
            console.error('❌ Unexpected error in fetchUserProfile:', error)
            return null
        }
    }

    // Helper to handle redirects based on user role
    const handleUserRedirect = (profile: UserProfile, currentPath: string) => {
        console.log('📍 Handling redirect for role:', profile.role, 'current path:', currentPath)

        // Only redirect if we're on the auth page or a non-dashboard page
        const shouldRedirect = currentPath === '/auth' ||
            currentPath === '/auth/' ||
            currentPath === '/admin-login' ||
            currentPath === '/' ||
            (!currentPath.startsWith('/admin/dashboard') &&
                !currentPath.startsWith('/agency/dashboard') &&
                !currentPath.startsWith('/user/dashboard'))

        if (!shouldRedirect) {
            console.log('📍 User already on appropriate dashboard, no redirect needed')
            return
        }

        // Redirect based on user role
        if (profile.role === 'admin' || profile.email === '<EMAIL>') {
            console.log('👑 Redirecting admin to /admin/dashboard')
            // Set admin cookie for middleware
            document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"
            console.log('🍪 Admin cookie set')
            try {
                router.push('/admin/dashboard')
                console.log('✅ Admin redirect initiated')
            } catch (error) {
                console.error('❌ Admin redirect failed:', error)
            }
        } else if (profile.role === 'agency') {
            console.log('🏢 Redirecting agency to /agency/dashboard')
            try {
                router.push('/agency/dashboard')
                console.log('✅ Agency redirect initiated')
            } catch (error) {
                console.error('❌ Agency redirect failed:', error)
            }
        } else if (profile.role === 'user') {
            console.log('👤 Redirecting user to /user/dashboard')
            try {
                router.push('/user/dashboard')
                console.log('✅ User redirect initiated')
            } catch (error) {
                console.error('❌ User redirect failed:', error)
            }
        } else {
            console.log('📍 Unknown role, redirecting to home')
            router.push('/')
        }
    }

    useEffect(() => {
        const getInitialSession = async () => {
            try {
                console.log('🔄 Initializing auth state...')
                const { data: { session } } = await supabase.auth.getSession()
                setSession(session)

                if (session?.user) {
                    console.log('👤 Found existing session for user:', session.user.id)
                    const profile = await fetchUserProfile(session.user.id)
                    if (profile) {
                        setUser({ ...session.user, ...profile } as any)
                        setUserProfile(profile)
                        setIsAuthenticated(true)
                        console.log('✅ User profile loaded successfully')

                        // Set admin cookie if user is admin
                        if (profile.role === 'admin' || profile.email === '<EMAIL>') {
                            document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"
                            console.log('🍪 Admin cookie set for existing session')
                        }

                        // Handle redirect for initial session
                        setTimeout(() => {
                            const currentPath = window.location.pathname
                            handleUserRedirect(profile, currentPath)
                        }, 500)
                    } else {
                        console.log('❌ Failed to load user profile')
                        setUser(null)
                        setUserProfile(null)
                        setIsAuthenticated(false)
                    }
                } else {
                    console.log('❌ No existing session found')
                    setUser(null)
                    setUserProfile(null)
                    setIsAuthenticated(false)
                }
            } catch (error) {
                console.error('❌ Error during auth initialization:', error)
                setUser(null)
                setUserProfile(null)
                setIsAuthenticated(false)
            } finally {
                setIsLoading(false)
            }
        }

        getInitialSession()

        const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
                console.log('🔄 Auth state change event:', event)
                setSession(session)

                if (session?.user) {
                    console.log('👤 User authenticated:', session.user.id)
                    const profile = await fetchUserProfile(session.user.id)
                    if (profile) {
                        setUser({ ...session.user, ...profile } as any)
                        setUserProfile(profile)
                        setIsAuthenticated(true)

                        // Set admin cookie if user is admin
                        if (profile.role === 'admin' || profile.email === '<EMAIL>') {
                            document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"
                            console.log('🍪 Admin cookie set for auth state change')
                        }

                        // Handle redirects for auth state changes (like page refresh)
                        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED' || event === 'INITIAL_SESSION') {
                            setTimeout(() => {
                                const currentPath = window.location.pathname
                                handleUserRedirect(profile, currentPath)
                            }, 500)
                        }
                    } else {
                        console.log('❌ Failed to load user profile after auth state change')
                        setUser(null)
                        setUserProfile(null)
                        setIsAuthenticated(false)
                    }
                } else {
                    console.log('❌ User signed out or no session')
                    setUser(null)
                    setUserProfile(null)
                    setIsAuthenticated(false)
                }
                setIsLoading(false)
            }
        )
        return () => subscription.unsubscribe()
    }, [])

    const login = async (email: string, password: string) => {
        setIsLoading(true)
        try {
            console.log('🔑 Attempting login for:', email)
            const { data, error } = await SupabaseAuthService.signIn(email, password)

            if (error) {
                console.error('❌ Login error:', error)
                setIsLoading(false)
                return { error }
            }

            // Fetch and set user profile after login
            if (data?.user) {
                console.log('✅ Login successful, fetching profile...')
                const profile = await fetchUserProfile(data.user.id)
                if (profile) {
                    setUser({ ...data.user, ...profile } as any)
                    setUserProfile(profile)
                    setIsAuthenticated(true)

                    console.log('✅ Login complete, user role:', profile.role)

                    // Set admin cookie if user is admin
                    if (profile.role === 'admin' || email === "<EMAIL>") {
                        document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"
                        console.log('🍪 Admin cookie set during login')
                    }

                    // Immediate redirect based on user role
                    const currentPath = window.location.pathname
                    if (currentPath === '/auth' || currentPath === '/auth/') {
                        if (profile.role === 'admin' || email === "<EMAIL>") {
                            console.log('👑 Redirecting admin to dashboard')
                            router.push('/admin/dashboard')
                        } else if (profile.role === 'agency') {
                            console.log('🏢 Redirecting agency to dashboard')
                            router.push('/agency/dashboard')
                        } else if (profile.role === 'user') {
                            console.log('👤 Redirecting user to dashboard')
                            router.push('/user/dashboard')
                        }
                    }
                } else {
                    console.error('❌ Failed to load user profile after login')
                    setIsLoading(false)
                    return { error: { message: 'Failed to load user profile' } }
                }
            }

            setIsLoading(false)
            return { error: null }
        } catch (error) {
            console.error("❌ Login failed", error)
            setIsLoading(false)
            return { error }
        }
    }

    const signUp = async (email: string, password: string, userData?: Partial<UserProfile>) => {
        setIsLoading(true)
        try {
            console.log('📝 Starting signup for:', email)
            const { data, error } = await SupabaseAuthService.signUp(email, password, userData || {})

            if (error) {
                console.error('❌ Signup error:', error)
                return { error }
            }

            // If signup is successful and user is created, try to sign them in
            if (data?.user) {
                console.log('✅ Signup successful, attempting auto-login...')
                // Wait a moment for the profile to be created
                await new Promise(resolve => setTimeout(resolve, 2000))

                // Try to sign in the user
                const signInResult = await SupabaseAuthService.signIn(email, password)
                if (signInResult.data?.user) {
                    const profile = await fetchUserProfile(signInResult.data.user.id)
                    if (profile) {
                        setUser({ ...signInResult.data.user, ...profile } as any)
                        setUserProfile(profile)
                        setIsAuthenticated(true)

                        // Set admin cookie if user is admin
                        if (profile.role === 'admin' || profile.email === '<EMAIL>') {
                            document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"
                            console.log('🍪 Admin cookie set during signup')
                        }

                        // Handle redirect based on user role
                        const currentPath = window.location.pathname
                        handleUserRedirect(profile, currentPath)

                        console.log('✅ Signup and auto-login successful, user role:', profile.role)
                    } else {
                        console.error('❌ Failed to load user profile after signup')
                        return { error: { message: 'Account created but failed to load profile. Please log in manually.' } }
                    }
                } else {
                    console.log('⚠️ Auto-login failed, user should log in manually')
                    return { error: { message: 'Account created successfully! Please log in to continue.' } }
                }
            }

            return { error: null }
        } catch (error) {
            console.error("❌ Sign up failed", error)
            return { error }
        } finally {
            setIsLoading(false)
        }
    }

    const logout = async () => {
        setIsLoading(true)
        let didRedirect = false

        // Determine redirect URL based on user role
        const redirectUrl = user?.role === 'admin' ? '/admin-login' : '/auth'

        const timeout = setTimeout(() => {
            if (!didRedirect) {
                router.push(redirectUrl)
                didRedirect = true
            }
        }, 2000)
        try {
            console.log('🚪 Logging out...')
            await SupabaseAuthService.signOut()
            localStorage.removeItem("adminAuthenticated")
            document.cookie = "adminAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
            setUser(null)
            setSession(null)
            setUserProfile(null)
            setIsAuthenticated(false)
            if (!didRedirect) {
                router.push(redirectUrl)
                didRedirect = true
            }
            console.log('✅ Logout successful')
        } catch (error) {
            console.error("❌ Logout failed", error)
            if (!didRedirect) {
                router.push(redirectUrl)
                didRedirect = true
            }
        } finally {
            clearTimeout(timeout)
            setIsLoading(false)
        }
    }

    const updateProfile = async (updates: Partial<UserProfile>) => {
        if (!user) return { error: new Error('No user logged in') }

        setIsLoading(true)
        try {
            const { data, error } = await SupabaseAuthService.updateUserProfile(user.id, updates)

            if (error) {
                return { error }
            }

            if (data) {
                setUser({ ...user, ...data })
                setUserProfile(data)
            }

            return { error: null }
        } catch (error) {
            console.error("❌ Profile update failed", error)
            return { error }
        } finally {
            setIsLoading(false)
        }
    }

    const updateUser = updateProfile // Alias for updateProfile

    const verifyEmail = async (token: string) => {
        try {
            const { error } = await supabase.auth.verifyOtp({
                token_hash: token,
                type: 'email'
            })
            return { error }
        } catch (error) {
            return { error }
        }
    }

    const markNotificationAsRead = async (notificationId: string) => {
        // Implementation would depend on your notification system
        console.log('Marking notification as read:', notificationId)
    }

    const isAgency = user?.role === 'agency'
    const isAdmin = user?.role === 'admin'

    return (
        <AuthContext.Provider value={{
            user,
            session,
            userProfile,
            login,
            signUp,
            logout,
            updateProfile,
            updateUser,
            verifyEmail,
            markNotificationAsRead,
            isAuthenticated,
            isLoading,
            isAgency,
            isAdmin
        }}>
            {children}
        </AuthContext.Provider>
    )
}

export function useAuth() {
    const context = useContext(AuthContext)
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider")
    }
    return context
}



# KriwDrive - Car Rental Platform

KriwDrive is a modern car rental platform built with Next.js, connecting travelers with trusted car rental agencies across Morocco.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

Verified agency profile
List unlimited cars
Fleet management dashboard
Receive booking requests
Accept/reject bookings
advanced analytics & reports
Customer support
enhanced listing visibility
Revenue tracking & insights
Automated booking confirmations
Customer review management
coupon code generation
your agency's link to share out of our platform

## Features

### For Users

- Browse and search available cars
- Book cars with flexible pricing options
- User dashboard for managing bookings
- Secure payment processing
- Real-time booking confirmations

### For Agencies

- Agency registration and verification
- Car fleet management
- Booking management dashboard
- Revenue tracking and analytics
- Customer communication tools

### For Administrators

- Complete admin dashboard
- User and agency management
- Car approval system
- Payment request handling
- Blog management system
- Analytics and reporting

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui components
- **Backend**: Supabase (Database, Auth, Storage)
- **Authentication**: Supabase Auth
- **File Upload**: Supabase Storage
- **Real-time**: Supabase Realtime

## Project Structure

The project follows a well-organized structure with:

- `app/` - Next.js App Router pages and API routes
- `components/` - Reusable React components organized by feature
- `lib/` - Utility functions and configurations
- `types/` - TypeScript type definitions
- `contexts/` - React context providers
- `hooks/` - Custom React hooks
- `config/` - Application configuration
- `utils/` - Utility constants and helpers

new row for relation "cars" violates check constraint "cars_fuel_type_check" we get this error when we edit car's inputs and click save changes.

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Note: RLS is automatically enabled in hosted Supabase
-- The jwt_secret is managed by Supabase automatically

-- =============================================
-- USERS TABLE (extends Supabase auth.users)
-- =============================================
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone TEXT,
    avatar TEXT,
    role TEXT CHECK (role IN ('user', 'agency', 'admin')) DEFAULT 'user',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AGENCIES TABLE
-- =============================================
CREATE TABLE public.agencies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE,
    agency_name TEXT NOT NULL,
    agency_description TEXT,
    agency_address TEXT,
    agency_phone TEXT,
    agency_email TEXT,
    agency_website TEXT,
    agency_logo TEXT,
    is_approved BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0,
    total_bookings INTEGER DEFAULT 0,
    location GEOGRAPHY(POINT),
    business_hours JSONB,
    -- Settings fields
    rental_terms TEXT,
    whatsapp TEXT,
    instagram TEXT,
    facebook TEXT,
    email_notifications BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT false,
    review_notifications BOOLEAN DEFAULT true,
    operating_hours_start TIME DEFAULT '08:00',
    operating_hours_end TIME DEFAULT '18:00',
    -- Policy fields
    min_driver_age INTEGER DEFAULT 21,
    min_rental_period INTEGER DEFAULT 1,
    free_cancellation_hours INTEGER DEFAULT 48,
    fuel_policy TEXT DEFAULT 'full_to_full',
    additional_terms TEXT,
    contract_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- CARS TABLE
-- =============================================
CREATE TABLE public.cars (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    brand TEXT NOT NULL,
    model TEXT NOT NULL,
    year INTEGER NOT NULL,
    license_plate TEXT UNIQUE NOT NULL,
    color TEXT NOT NULL,
    fuel_type TEXT CHECK (fuel_type IN ('gasoline', 'diesel', 'electric', 'hybrid', 'plug-in_hybrid', 'hydrogen')) NOT NULL,
    transmission TEXT CHECK (transmission IN ('manual', 'automatic', 'semi_automatic')) NOT NULL,
    seats INTEGER NOT NULL,
    doors INTEGER NOT NULL,
    daily_rate DECIMAL(10,2) NOT NULL,
    weekly_rate DECIMAL(10,2),
    monthly_rate DECIMAL(10,2),
    status TEXT CHECK (status IN ('available', 'rented', 'maintenance', 'unavailable', 'reserved')) DEFAULT 'available',
    description TEXT,
    features TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    location GEOGRAPHY(POINT),
    address TEXT,
    mileage INTEGER DEFAULT 0,
    insurance_info JSONB,
    gps_device_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- BOOKINGS TABLE
-- =============================================
CREATE TABLE public.bookings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE, -- Made nullable for guest bookings
    car_id UUID REFERENCES public.cars(id) ON DELETE CASCADE NOT NULL,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    pickup_location TEXT NOT NULL,
    return_location TEXT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    status TEXT CHECK (status IN ('pending', 'confirmed', 'active', 'completed', 'cancelled', 'rejected')) DEFAULT 'pending',
    payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded', 'partially_refunded')) DEFAULT 'pending',
    special_requests TEXT,
    -- Guest booking fields (for bookings without user registration)
    guest_name TEXT,
    guest_email TEXT,
    guest_phone TEXT,
    guest_address TEXT,
    is_guest_booking BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure either user_id is provided OR guest info is provided
    CONSTRAINT booking_user_or_guest CHECK (
        (user_id IS NOT NULL AND is_guest_booking = FALSE) OR
        (user_id IS NULL AND is_guest_booking = TRUE AND guest_name IS NOT NULL AND guest_email IS NOT NULL)
    )
);

-- =============================================
-- PAYMENTS TABLE
-- =============================================
CREATE TABLE public.payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'MAD',
    payment_method TEXT NOT NULL,
    transaction_id TEXT UNIQUE,
    status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'refunded')) DEFAULT 'pending',
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- REVIEWS TABLE
-- =============================================
CREATE TABLE public.reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    car_id UUID REFERENCES public.cars(id) ON DELETE CASCADE NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- COUPONS TABLE
-- =============================================
CREATE TABLE public.coupons (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    code TEXT UNIQUE NOT NULL,
    discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed')) NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_discount DECIMAL(10,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- GPS TRACKING TABLE
-- =============================================
CREATE TABLE public.gps_tracking (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    car_id UUID REFERENCES public.cars(id) ON DELETE CASCADE NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    speed DECIMAL(5,2),
    heading DECIMAL(5,2),
    battery_level INTEGER,
    is_online BOOLEAN DEFAULT TRUE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- NOTIFICATIONS TABLE
-- =============================================
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT CHECK (type IN ('booking', 'payment', 'review', 'system')) NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- BLOGS TABLE
-- =============================================
CREATE TABLE public.blogs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image TEXT,
    slug TEXT UNIQUE NOT NULL,
    status TEXT CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    author_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    tags TEXT[] DEFAULT '{}',
    meta_title TEXT,
    meta_description TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- MESSAGES TABLE (Contact Us & Payment Messages)
-- =============================================
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT CHECK (type IN ('contact', 'payment', 'support')) DEFAULT 'contact',
    status TEXT CHECK (status IN ('unread', 'read', 'replied', 'archived')) DEFAULT 'unread',
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    admin_notes TEXT,
    replied_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AGENCY DOCUMENTS TABLE
-- =============================================
CREATE TABLE public.agency_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    document_type TEXT CHECK (document_type IN ('license', 'insurance', 'registration', 'verification', 'other')) NOT NULL,
    document_name TEXT NOT NULL,
    document_url TEXT NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE, -- For documents that should appear on reservation page
    verified_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users indexes
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);

-- Agencies indexes
CREATE INDEX idx_agencies_user_id ON public.agencies(user_id);
CREATE INDEX idx_agencies_approved ON public.agencies(is_approved);
CREATE INDEX idx_agencies_location ON public.agencies USING GIST(location);

-- Cars indexes
CREATE INDEX idx_cars_agency_id ON public.cars(agency_id);
CREATE INDEX idx_cars_status ON public.cars(status);
CREATE INDEX idx_cars_location ON public.cars USING GIST(location);
CREATE INDEX idx_cars_brand_model ON public.cars(brand, model);
CREATE INDEX idx_cars_price ON public.cars(daily_rate);

-- Bookings indexes
CREATE INDEX idx_bookings_user_id ON public.bookings(user_id);
CREATE INDEX idx_bookings_agency_id ON public.bookings(agency_id);
CREATE INDEX idx_bookings_car_id ON public.bookings(car_id);
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_bookings_dates ON public.bookings(start_date, end_date);

-- Reviews indexes
CREATE INDEX idx_reviews_agency_id ON public.reviews(agency_id);
CREATE INDEX idx_reviews_car_id ON public.reviews(car_id);
CREATE INDEX idx_reviews_status ON public.reviews(status);

-- GPS tracking indexes
CREATE INDEX idx_gps_tracking_car_id ON public.gps_tracking(car_id);
CREATE INDEX idx_gps_tracking_timestamp ON public.gps_tracking(timestamp);

-- Blogs indexes
CREATE INDEX idx_blogs_status ON public.blogs(status);
CREATE INDEX idx_blogs_published_at ON public.blogs(published_at);
CREATE INDEX idx_blogs_slug ON public.blogs(slug);
CREATE INDEX idx_blogs_author_id ON public.blogs(author_id);

-- Messages indexes
CREATE INDEX idx_messages_status ON public.messages(status);
CREATE INDEX idx_messages_type ON public.messages(type);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);

-- Agency documents indexes
CREATE INDEX idx_agency_documents_agency_id ON public.agency_documents(agency_id);
CREATE INDEX idx_agency_documents_type ON public.agency_documents(document_type);
CREATE INDEX idx_agency_documents_verified ON public.agency_documents(is_verified);
CREATE INDEX idx_agency_documents_public ON public.agency_documents(is_public);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gps_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_documents ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Admin policies for users
CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update all users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can delete users" ON public.users
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Agencies policies
CREATE POLICY "Agencies are viewable by all" ON public.agencies
    FOR SELECT USING (true);

CREATE POLICY "Agency owners can update their agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Agency owners can insert their agency" ON public.agencies
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow authenticated users to insert agencies (for registration)
CREATE POLICY "Authenticated users can register as agencies" ON public.agencies
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Admin policies for agencies
CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Cars policies
CREATE POLICY "Cars are viewable by all" ON public.cars
    FOR SELECT USING (true);

CREATE POLICY "Agency owners can manage their cars" ON public.cars
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admin policies for cars
CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Agency owners can view their agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (auth.uid() = user_id OR is_guest_booking = true);

CREATE POLICY "Guest bookings can be created" ON public.bookings
    FOR INSERT WITH CHECK (is_guest_booking = true AND user_id IS NULL);

CREATE POLICY "Agency owners can update their agency bookings" ON public.bookings
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admin policies for bookings
CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Reviews policies
CREATE POLICY "Reviews are viewable by all" ON public.reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can create reviews for their bookings" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        booking_id IN (
            SELECT id FROM public.bookings
            WHERE user_id = auth.uid() AND status = 'completed'
        )
    );

CREATE POLICY "Agency owners can update their agency reviews" ON public.reviews
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admin policies for reviews
CREATE POLICY "Admins can manage all reviews" ON public.reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Coupons policies
CREATE POLICY "Coupons are viewable by all" ON public.coupons
    FOR SELECT USING (true);

CREATE POLICY "Agency owners can manage their coupons" ON public.coupons
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admin policies for coupons
CREATE POLICY "Admins can manage all coupons" ON public.coupons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- GPS tracking policies
CREATE POLICY "Agency owners can view their cars GPS data" ON public.gps_tracking
    FOR SELECT USING (
        car_id IN (
            SELECT c.id FROM public.cars c
            JOIN public.agencies a ON c.agency_id = a.id
            WHERE a.user_id = auth.uid()
        )
    );

-- Payments policies
CREATE POLICY "Users can view their booking payments" ON public.payments
    FOR SELECT USING (
        booking_id IN (
            SELECT id FROM public.bookings WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Agency owners can view their agency payments" ON public.payments
    FOR SELECT USING (
        booking_id IN (
            SELECT b.id FROM public.bookings b
            JOIN public.agencies a ON b.agency_id = a.id
            WHERE a.user_id = auth.uid()
        )
    );

-- Admin policies for payments
CREATE POLICY "Admins can manage all payments" ON public.payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Admin policies for notifications
CREATE POLICY "Admins can manage all notifications" ON public.notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- BLOGS POLICIES
-- =============================================
CREATE POLICY "Published blogs are viewable by all" ON public.blogs
    FOR SELECT USING (status = 'published');

CREATE POLICY "Admins can manage all blogs" ON public.blogs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- MESSAGES POLICIES
-- =============================================
CREATE POLICY "Anyone can create messages" ON public.messages
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can manage all messages" ON public.messages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- AGENCY DOCUMENTS POLICIES
-- =============================================
CREATE POLICY "Public documents are viewable by all" ON public.agency_documents
    FOR SELECT USING (is_public = true);

CREATE POLICY "Agency owners can view their documents" ON public.agency_documents
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Agency owners can manage their documents" ON public.agency_documents
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all agency documents" ON public.agency_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agencies_updated_at BEFORE UPDATE ON public.agencies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cars_updated_at BEFORE UPDATE ON public.cars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON public.reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blogs_updated_at BEFORE UPDATE ON public.blogs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agency_documents_updated_at BEFORE UPDATE ON public.agency_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, first_name, last_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'role', 'user')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update agency stats
CREATE OR REPLACE FUNCTION update_agency_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE public.agencies
        SET 
            total_bookings = (
                SELECT COUNT(*) FROM public.bookings 
                WHERE agency_id = NEW.agency_id AND status = 'completed'
            ),
            rating = (
                SELECT AVG(rating) FROM public.reviews r
                JOIN public.bookings b ON r.booking_id = b.id
                WHERE b.agency_id = NEW.agency_id AND r.status = 'approved'
            )
        WHERE id = NEW.agency_id;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE public.agencies
        SET 
            total_bookings = (
                SELECT COUNT(*) FROM public.bookings 
                WHERE agency_id = OLD.agency_id AND status = 'completed'
            ),
            rating = (
                SELECT AVG(rating) FROM public.reviews r
                JOIN public.bookings b ON r.booking_id = b.id
                WHERE b.agency_id = OLD.agency_id AND r.status = 'approved'
            )
        WHERE id = OLD.agency_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for agency stats updates
CREATE TRIGGER update_agency_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_agency_stats();

-- =============================================
-- STORAGE BUCKETS
-- =============================================

-- Create storage buckets for file uploads (skip if they already exist)
INSERT INTO storage.buckets (id, name, public)
SELECT 'car-images', 'car-images', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'car-images');

INSERT INTO storage.buckets (id, name, public)
SELECT 'agency-logos', 'agency-logos', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'agency-logos');

INSERT INTO storage.buckets (id, name, public)
SELECT 'avatars', 'avatars', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'avatars');

INSERT INTO storage.buckets (id, name, public)
SELECT 'documents', 'documents', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'documents');

INSERT INTO storage.buckets (id, name, public)
SELECT 'documents', 'documents', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'documents');

-- Storage policies for car images
CREATE POLICY "Car images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'car-images');

CREATE POLICY "Agency owners can upload car images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'car-images' AND
        auth.uid() IN (
            SELECT a.user_id FROM public.agencies a
            JOIN public.cars c ON a.id = c.agency_id
            WHERE c.id::text = (storage.foldername(name))[1]
        )
    );

CREATE POLICY "Agency owners can delete their car images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'car-images' AND
        auth.uid() IN (
            SELECT a.user_id FROM public.agencies a
            JOIN public.cars c ON a.id = c.agency_id
            WHERE c.id::text = (storage.foldername(name))[1]
        )
    );

-- Storage policies for agency logos
CREATE POLICY "Agency logos are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'agency-logos');

CREATE POLICY "Agency owners can upload their logo" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'agency-logos' AND
        auth.uid() IN (
            SELECT user_id FROM public.agencies
            WHERE id::text = (storage.foldername(name))[1]
        )
    );

-- Storage policies for user avatars
CREATE POLICY "User avatars are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Storage policies for documents
CREATE POLICY "Users can view their own documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can upload their own documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Admin storage policies
CREATE POLICY "Admins can manage all storage objects" ON storage.objects
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- SAMPLE DATA (Optional)
-- =============================================

-- Note: Admin user should be created through Supabase Auth first, then this profile will be created automatically
-- You can manually create an admin user in Supabase Auth dashboard with email: <EMAIL>
-- The trigger will automatically create the profile in public.users table

-- Alternatively, you can insert a sample admin user if you have the UUID from auth.users:
-- INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
-- VALUES (
--     'your-admin-uuid-from-auth-users',
--     '<EMAIL>',
--     'Admin',
--     'User',
--     'admin',
--     true
-- );
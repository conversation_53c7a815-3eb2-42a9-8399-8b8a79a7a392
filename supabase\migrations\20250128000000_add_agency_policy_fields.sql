-- Add policy and settings fields to agencies table
ALTER TABLE public.agencies 
ADD COLUMN IF NOT EXISTS rental_terms TEXT,
ADD COLUMN IF NOT EXISTS whatsapp TEXT,
ADD COLUMN IF NOT EXISTS instagram TEXT,
ADD COLUMN IF NOT EXISTS facebook TEXT,
ADD COLUMN IF NOT EXISTS email_notifications B<PERSON><PERSON>EAN DEFAULT true,
ADD COLUMN IF NOT EXISTS sms_notifications BO<PERSON>EAN DEFAULT false,
ADD COLUMN IF NOT EXISTS review_notifications BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS operating_hours_start TIME DEFAULT '08:00',
ADD COLUMN IF NOT EXISTS operating_hours_end TIME DEFAULT '18:00',
ADD COLUMN IF NOT EXISTS min_driver_age INTEGER DEFAULT 21,
ADD COLUMN IF NOT EXISTS min_rental_period INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS free_cancellation_hours INTEGER DEFAULT 48,
ADD COLUMN IF NOT EXISTS fuel_policy TEXT DEFAULT 'full_to_full',
ADD COLUMN IF NOT EXISTS additional_terms TEXT,
ADD COLUMN IF NOT EXISTS contract_url TEXT;

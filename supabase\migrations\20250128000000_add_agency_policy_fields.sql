-- Add policy and settings fields to agencies table
ALTER TABLE public.agencies
ADD COLUMN IF NOT EXISTS rental_terms TEXT,
ADD COLUMN IF NOT EXISTS whatsapp TEXT,
ADD COLUMN IF NOT EXISTS instagram TEXT,
ADD COLUMN IF NOT EXISTS facebook TEXT,
ADD COLUMN IF NOT EXISTS email_notifications B<PERSON><PERSON><PERSON>N DEFAULT true,
ADD COLUMN IF NOT EXISTS sms_notifications BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS review_notifications BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS operating_hours_start TIME DEFAULT '08:00',
ADD COLUMN IF NOT EXISTS operating_hours_end TIME DEFAULT '18:00',
ADD COLUMN IF NOT EXISTS min_driver_age INTEGER DEFAULT 21,
ADD COLUMN IF NOT EXISTS min_rental_period INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS free_cancellation_hours INTEGER DEFAULT 48,
ADD COLUMN IF NOT EXISTS fuel_policy TEXT DEFAULT 'full_to_full',
ADD COLUMN IF NOT EXISTS additional_terms TEXT,
ADD COLUMN IF NOT EXISTS contract_url TEXT;

-- Fix cars table - add body_type column and remove unnecessary columns
ALTER TABLE public.cars
ADD COLUMN IF NOT EXISTS body_type TEXT,
DROP COLUMN IF EXISTS category;

-- Remove license_plate, seats, doors constraints since we're removing these fields
ALTER TABLE public.cars
ALTER COLUMN license_plate DROP NOT NULL,
ALTER COLUMN seats DROP NOT NULL,
ALTER COLUMN doors DROP NOT NULL;

-- Add car-images storage bucket
INSERT INTO storage.buckets (id, name, public)
SELECT 'car-images', 'car-images', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'car-images');

-- Add documents storage bucket
INSERT INTO storage.buckets (id, name, public)
SELECT 'documents', 'documents', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'documents');

-- Add agency-logos storage bucket
INSERT INTO storage.buckets (id, name, public)
SELECT 'agency-logos', 'agency-logos', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'agency-logos');

"use client"

import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  StarIcon,
  MapPin,
  Car,
  Calendar,
  Clock,
  CheckCircle,
  Award,
  ShieldCheck,
  Phone,
  Mail,
  Instagram,
  Facebook,
  MessageCircle,
  FileText,
  Users,
} from "lucide-react"
import { FeaturedCarCard } from "@/components/features/car/featured-car-card"
import { toast } from "sonner"

export default function AgencyProfilePage() {
  const params = useParams()
  const agencyName = params.agencyName as string
  const supabase = createClientComponentClient()
  
  const [agency, setAgency] = useState<any>(null)
  const [cars, setCars] = useState<any[]>([])
  const [reviews, setReviews] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)

  useEffect(() => {
    loadAgencyProfile()
  }, [agencyName])

  const loadAgencyProfile = async () => {
    try {
      setLoading(true)
      
      // Convert slug back to agency name (reverse the slug process)
      const decodedAgencyName = agencyName.replace(/-/g, ' ')
      
      // Find agency by name (case insensitive)
      const { data: agencyData, error: agencyError } = await supabase
        .from('agencies')
        .select('*')
        .ilike('agency_name', decodedAgencyName)
        .eq('is_approved', true)
        .single()

      if (agencyError || !agencyData) {
        console.error('Agency not found:', agencyError)
        setNotFound(true)
        return
      }

      setAgency(agencyData)

      // Load agency's cars
      const { data: carsData, error: carsError } = await supabase
        .from('cars')
        .select('*')
        .eq('agency_id', agencyData.id)
        .eq('status', 'available')
        .order('created_at', { ascending: false })

      if (carsError) {
        console.error('Error loading cars:', carsError)
      } else {
        setCars(carsData || [])
      }

      // Load agency reviews
      const { data: reviewsData, error: reviewsError } = await supabase
        .from('reviews')
        .select(`
          *,
          users (
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('agency_id', agencyData.id)
        .eq('status', 'approved')
        .order('created_at', { ascending: false })
        .limit(10)

      if (reviewsError) {
        console.error('Error loading reviews:', reviewsError)
      } else {
        setReviews(reviewsData || [])
      }

    } catch (error) {
      console.error('Error loading agency profile:', error)
      toast.error('Failed to load agency profile')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg">Loading agency profile...</p>
        </div>
      </div>
    )
  }

  if (notFound || !agency) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Agency Not Found</h1>
          <p className="text-gray-600 mb-8">The agency you're looking for doesn't exist or is not approved.</p>
          <Button onClick={() => window.history.back()}>Go Back</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-start space-x-6">
            <Avatar className="h-24 w-24">
              <AvatarImage src={agency.agency_logo} alt={agency.agency_name} />
              <AvatarFallback className="text-2xl">
                {agency.agency_name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{agency.agency_name}</h1>
                {agency.is_approved && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center">
                  <StarIcon className="h-4 w-4 fill-current text-yellow-500 mr-1" />
                  <span className="font-medium">{agency.rating || 4.5}</span>
                  <span className="text-gray-500 ml-1">({reviews.length} reviews)</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span>{agency.agency_address || 'Morocco'}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Car className="h-4 w-4 mr-1" />
                  <span>{cars.length} cars</span>
                </div>
              </div>
              <p className="text-gray-600 max-w-2xl">
                {agency.agency_description || 'Professional car rental services with quality vehicles and excellent customer service.'}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="cars" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="cars">Cars ({cars.length})</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
            <TabsTrigger value="reviews">Reviews ({reviews.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="cars" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cars.map((car) => (
                <FeaturedCarCard
                  key={car.id}
                  id={car.id}
                  title={`${car.brand} ${car.model}`}
                  price={car.daily_rate}
                  location={car.address || 'Morocco'}
                  images={car.images || ["/placeholder.svg"]}
                  rating={4.5}
                  reviews={0}
                  category={car.category || 'Car'}
                />
              ))}
            </div>
            {cars.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No cars available at the moment.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="about" className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-4">About {agency.agency_name}</h3>
                <p className="text-gray-600 mb-6">
                  {agency.agency_description || 'Professional car rental services with quality vehicles and excellent customer service.'}
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Contact Information</h4>
                    <div className="space-y-2">
                      {agency.agency_phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{agency.agency_phone}</span>
                        </div>
                      )}
                      {agency.agency_email && (
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{agency.agency_email}</span>
                        </div>
                      )}
                      {agency.whatsapp && (
                        <div className="flex items-center">
                          <MessageCircle className="h-4 w-4 mr-2 text-green-600" />
                          <span>{agency.whatsapp}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3">Rental Policy</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div>Minimum age: {agency.min_driver_age || 21} years</div>
                      <div>Minimum rental: {agency.min_rental_period || 1} day(s)</div>
                      <div>Free cancellation: {agency.free_cancellation_hours || 48} hours before</div>
                      <div>Fuel policy: {agency.fuel_policy || 'Full to Full'}</div>
                    </div>
                  </div>
                </div>

                {agency.contract_url && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold mb-2">Agency Contract</h4>
                    <a
                      href={agency.contract_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 hover:text-blue-700"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      View Contract
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-6">
            <div className="space-y-4">
              {reviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Avatar>
                        <AvatarImage src={review.users?.avatar_url} />
                        <AvatarFallback>
                          {review.users?.first_name?.charAt(0)}{review.users?.last_name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold">
                            {review.users?.first_name} {review.users?.last_name}
                          </h4>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating ? 'fill-current text-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-600">{review.comment}</p>
                        <p className="text-sm text-gray-500 mt-2">
                          {new Date(review.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            {reviews.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <StarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No reviews yet.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
